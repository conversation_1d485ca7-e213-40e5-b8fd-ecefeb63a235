import requests
import json

# === CONFIGURATION ===
# Option 1: Full trigger URL with key
function_url = "https://emailagent-c2emczdnc2c8dbd5.centralindia-01.azurewebsites.net/api/sendEmail?code=YOUR_FUNCTION_KEY"

# Option 2: If you want to build it manually:
# base_url = "https://emailagent-c2emczdnc2c8dbd5.centralindia-01.azurewebsites.net"
# function_name = "sendEmail"
# function_key = "YOUR_FUNCTION_KEY"
# function_url = f"{base_url}/api/{function_name}?code={function_key}"

# === Payload to send to the function ===
payload = {
    "to": "<EMAIL>",
    "subject": "Hello from Python",
    "body": "This is a test email triggered from a Python script using Azure Function."
}

headers = {
    "Content-Type": "application/json"
}

# === Send Request ===
try:
    response = requests.post(function_url, headers=headers, data=json.dumps(payload))
    print("Status Code:", response.status_code)
    print("Response Body:", response.text)
except Exception as e:
    print("Error occurred:", str(e))
